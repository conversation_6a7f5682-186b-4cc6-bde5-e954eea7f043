<?php

function registerBlocks() {


    // Always try manual registration first for hero-simple
    $result = register_block_type('webeon-blocks/hero-simple', array(
        'title' => 'Hero Simple',
        'description' => 'A simple hero block for displaying prominent messages',
        'icon' => 'heading',
        'category' => 'webeon-blocks',
        'keywords' => array('hero', 'simple', 'webeon', 'Главный', 'Заглавный'),
        'render_callback' => function($attributes) {
            $message = $attributes['message'] ?? 'Hello World!';
            $wrapper_attributes = get_block_wrapper_attributes(array('class' => 'hero-simple'));

            return sprintf(
                '<div %s><div class="hero-simple-content"><div class="message">%s</div></div></div>',
                $wrapper_attributes,
                esc_html($message)
            );
        },
        'attributes' => array(
            'message' => array(
                'type' => 'string',
                'default' => 'Hello World!'
            )
        ),
        'supports' => array(
            'align' => array('wide', 'full'),
            'color' => array(
                'text' => true,
                'background' => true
            ),
            'typography' => array(
                'fontSize' => true,
                'textAlign' => true
            )
        ),
        'editor_script' => 'webeon-hero-simple-editor',
        'style' => 'webeon-hero-simple-style'
    ));

    // Register assets
    wp_register_script(
        'webeon-hero-simple-editor',
        THEME_URL . '/gutenberg/blocks/hero-simple/editor.js',
        array('wp-blocks', 'wp-block-editor', 'wp-element'),
        filemtime(THEME_PATH . '/gutenberg/blocks/hero-simple/editor.js')
    );

    wp_register_style(
        'webeon-hero-simple-style',
        THEME_URL . '/gutenberg/blocks/hero-simple/style.css',
        array(),
        filemtime(THEME_PATH . '/gutenberg/blocks/hero-simple/style.css')
    );

    // Debug logging
    error_log('Hero Simple Block registration result: ' . ($result ? 'SUCCESS' : 'FAILED'));
    if ($result) {
        error_log('Block name: ' . $result->name);
        error_log('Block title: ' . $result->title);
    }

    // Register other blocks automatically
    $dir = __DIR__ . '/blocks/';
    $folders = array_diff(scandir($dir), array('..', '.', 'hero-simple'));

    foreach ($folders as $folder) {
        if (is_dir($dir . $folder)) {
            $result = register_block_type($dir . $folder);

            // Debug: Log block registration
            if (WP_DEBUG && $result) {
                error_log('Webeon Block registered: ' . $result->name);
            }
        }
    }

}

add_action( 'init', 'registerBlocks', 5 );

// Test function to verify block registration is working
function testBlockRegistration() {
    $registry = WP_Block_Type_Registry::get_instance();
    $all_blocks = $registry->get_all_registered();
    $webeon_blocks = array_filter(array_keys($all_blocks), function($block) {
        return strpos($block, 'webeon-blocks/') === 0;
    });

    error_log('Test: Webeon blocks in registry: ' . implode(', ', $webeon_blocks));
    error_log('Total blocks registered: ' . count($all_blocks));

    // Check if hero-simple specifically exists
    if (isset($all_blocks['webeon-blocks/hero-simple'])) {
        error_log('Hero Simple block found in registry!');
    } else {
        error_log('Hero Simple block NOT found in registry!');
    }
}
add_action('init', 'testBlockRegistration', 15);

// Uncomment the lines below for debugging if needed
// function webeon_blocks_admin_notice() {
//     if (current_user_can('manage_options')) {
//         $registry = WP_Block_Type_Registry::get_instance();
//         $all_blocks = $registry->get_all_registered();
//
//         if (isset($all_blocks['webeon-blocks/hero-simple'])) {
//             echo '<div class="notice notice-success"><p>✅ Hero Simple block is registered successfully!</p></div>';
//         } else {
//             echo '<div class="notice notice-error"><p>❌ Hero Simple block registration failed!</p></div>';
//         }
//     }
// }
// add_action('admin_notices', 'webeon_blocks_admin_notice');


function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');