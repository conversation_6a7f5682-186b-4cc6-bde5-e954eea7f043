(function() {
    'use strict';

    // Register the block
    wp.blocks.registerBlockType('webeon-blocks/wrapper', {
        edit: function(props) {
            var blockProps = wp.blockEditor.useBlockProps({
                className: 'wrapper-section'
            });

            return wp.element.createElement(
                'div',
                blockProps,
                wp.element.createElement(
                    'div',
                    { className: 'wrapper' },
                    wp.element.createElement(
                        'div',
                        { className: 'wrapper-content' },
                        wp.element.createElement(wp.blockEditor.InnerBlocks, {
                            allowedBlocks: true,
                            template: [
                                ['core/paragraph', { placeholder: 'Add content inside the wrapper...' }]
                            ]
                        })
                    )
                )
            );
        },

        save: function() {
            // Return null for server-side rendering
            return wp.element.createElement(wp.blockEditor.InnerBlocks.Content);
        }
    });
})();
