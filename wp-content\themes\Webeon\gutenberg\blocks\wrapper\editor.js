// Editor functionality for wrapper block
(function() {
    'use strict';

    // Wait for WordPress to be ready
    wp.domReady(function() {
        // Modify the existing block registration to add edit and save functions
        wp.hooks.addFilter(
            'blocks.registerBlockType',
            'webeon-blocks/wrapper',
            function(settings, name) {
                if (name === 'webeon-blocks/wrapper') {
                    settings.edit = function(props) {
                        var blockProps = wp.blockEditor.useBlockProps({
                            className: 'wrapper-section'
                        });

                        return wp.element.createElement(
                            'div',
                            blockProps,
                            wp.element.createElement(
                                'div',
                                { className: 'wrapper' },
                                wp.element.createElement(
                                    'div',
                                    { className: 'wrapper-content' },
                                    wp.element.createElement(wp.blockEditor.InnerBlocks, {
                                        template: [
                                            ['core/paragraph', { placeholder: 'Add content inside the wrapper...' }]
                                        ]
                                    })
                                )
                            )
                        );
                    };

                    settings.save = function() {
                        var blockProps = wp.blockEditor.useBlockProps.save({
                            className: 'wrapper-section'
                        });

                        return wp.element.createElement(
                            'div',
                            blockProps,
                            wp.element.createElement(
                                'div',
                                { className: 'wrapper' },
                                wp.element.createElement(
                                    'div',
                                    { className: 'wrapper-content' },
                                    wp.element.createElement(wp.blockEditor.InnerBlocks.Content)
                                )
                            )
                        );
                    };
                }
                return settings;
            }
        );
    });
})();
