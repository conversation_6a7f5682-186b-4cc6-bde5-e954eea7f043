(function() {
    'use strict';

    // Register the block
    wp.blocks.registerBlockType('webeon-blocks/wrapper', {
        edit: function() {
            return wp.element.createElement(
                'div',
                {
                    className: 'wrapper wp-block-webeon-blocks-wrapper'
                },
                wp.element.createElement(
                    'div',
                    { className: 'wrapper-content' },
                    wp.element.createElement(wp.blockEditor.InnerBlocks)
                )
            );
        },

        save: function() {
            // Return null for server-side rendering
            return null;
        }
    });
})();
