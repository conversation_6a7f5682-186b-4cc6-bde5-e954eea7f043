<?php

define("ADMIN_AJAX_URL", admin_url("admin-ajax.php"));

define("THEME_PATH", get_template_directory());
define("THEME_URL", get_template_directory_uri());

define("FRONTEND_PATH", THEME_PATH . "/frontend");
define("FRONTEND_URL", THEME_URL . "/frontend");

define("ADMIN_LOCALE", get_user_locale());

function addFilesFromFolder(string $path)
{
    $dir = THEME_PATH . '/' . $path;
    $filesList = scandir($dir);
    foreach ($filesList as $fileName) {
        if ($fileName !== '.' && $fileName !== '..') {
            $filePath = $dir . '/' . $fileName;
            if (is_file($filePath)) {
                include_once($filePath);
            }
        }
    }
}

// classes
addFilesFromFolder('/classes');

function register_layout_category($categories)
    {

        $customBlocksToBegin = [
            [
                'slug'  => 'webeon-blocks',
                'title' => 'WebEon Blocks'
            ],
        ];

        $categoriesSorted = [];

        foreach ($customBlocksToBegin as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        foreach ($categories as $category) {
            $categoriesSorted[] = $category;
        }

        $customBlocksToEnd = [
            [
                'slug'  => 'webeon-service-blocks',
                'title' => 'WebEon Service Blocks'
            ],
        ];

        foreach ($customBlocksToEnd as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        return $categoriesSorted;
        // return $categories;
    }

    add_filter('block_categories_all', 'register_layout_category', 10, 2);

// Register native Gutenberg blocks (independent of ACF)
include_once(THEME_PATH . '/gutenberg/registerblocks.php');

// Test block registration removed - registration is working

// Diagnostic code removed - issue was file path case sensitivity

// acf
if (function_exists('get_field')) {


    // include_once(THEME_PATH . '/acf/registerOptionsPages.php');
    include_once(THEME_PATH . '/gutenberg/registerblocks.php');
    addFilesFromFolder('/acf/fields');
    
    // define('GENERAL_SETTINGS', get_field('general_settings_group', 'options'));
    // define('VIEW_SETTINGS', get_field('theme_view_group', 'options'));
    // define('ADDITIONAL_CODE', get_field('additional_code_group', 'options'));
    // define('CONTACTS', get_field('contacts_settings_group', 'options'));

    $fontFamilySetting = VIEW_SETTINGS['theme_font_family'] ?? 'Roboto';
    $secondFontFamilySetting = VIEW_SETTINGS['theme_second_font_family'] ?? 'none';

    $webSafeFonts = [
        'Arial',
        'Tahoma',
        'Helvetica',
    ];

    function convertFontFamilyNameToCss($fontFamilyName){
        return match ($fontFamilyName) {
            'BlenderPro' => 'Blender Pro',
            'Inter' => 'Inter',
            'NotoSans' => 'Noto Sans',
            'OpenSans' => 'Open Sans',
            'Ubuntu' => 'Ubuntu',
            'Montserrat' => 'Montserrat',
            'HelveticaNeue' => 'Helvetica Neue',
            'HelveticaNeueCondensedBold' => 'Helvetica Neue Condensed Bold',
            'Arial' => 'Arial',
            'Tahoma' => 'Tahoma',
            'Helvetica' => 'Helvetica',
            default => 'Roboto',
        };
    }

    $fontFamily = strval(convertFontFamilyNameToCss($fontFamilySetting));
    if(!in_array($fontFamily, $webSafeFonts)){
        $fontsArrayUrls[$fontFamily] = FRONTEND_URL . '/fonts/'. $fontFamilySetting .'/fonts.css';
    }
    $fontsArrayNames[] = $fontFamily;

    if($secondFontFamilySetting && $secondFontFamilySetting != 'none'){
        $secondFontFamily = strval(convertFontFamilyNameToCss($secondFontFamilySetting));
        if(!in_array($secondFontFamily, $webSafeFonts)){
            $fontsArrayUrls[$secondFontFamily] = FRONTEND_URL . '/fonts/'. $secondFontFamilySetting .'/fonts.css';
        }
        $fontsArrayNames[] = $secondFontFamily;
    }

    if(!in_array('Arial', $fontsArrayNames)){
        $fontsArrayNames[] = 'Arial';
    }

    define('FONTSARRAYURLS', $fontsArrayUrls);
    define('FONTSARRAYNAMES', $fontsArrayNames);
}

// wordpress hooks
addFilesFromFolder('/hooks');