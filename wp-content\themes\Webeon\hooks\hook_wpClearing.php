<?php

remove_theme_support( 'core-block-patterns' );

//disable default gutenberg frontend
function removeGuttenbergBlockCssAndJquery()
{
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-blocks-style');
    wp_dequeue_style('storefront-gutenberg-blocks');
    wp_dequeue_style('global-styles'); // REMOVE THEME.JSON
}

add_action('wp_enqueue_scripts', 'removeGuttenbergBlockCssAndJquery', 100);

remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );

//disable default gutenberg blocks
add_filter('allowed_block_types_all', function ($allowed_block_types, $block_editor_context) {
    $registry = WP_Block_Type_Registry::get_instance();
	$registerdBlocks = $registry->get_all_registered();
	$registerdBblocksArray = array_keys($registerdBlocks);

	// Debug: Log registered blocks
	if (WP_DEBUG) {
		$webeonBlocks = array_filter($registerdBblocksArray, function($block) {
			return strpos($block, 'webeon-blocks/') === 0;
		});
		if (!empty($webeonBlocks)) {
			error_log('Webeon blocks found in registry: ' . implode(', ', $webeonBlocks));
		} else {
			error_log('No webeon-blocks found in registry. Total blocks: ' . count($registerdBblocksArray));
		}
	}

    $blocksToRemove = [
        'core/details',
        'core/preformatted',
        'core/verse',
        'core/gallery',
        'core/audio',
        'core/cover',
        'core/columns',
        'core/group',
        'core/row',
        'core/stack',
        'core/more',
        'core/nextpage',
        'core/spacer',
        'core/archives',
        'core/calendar',
        'core/categories',
        'core/latest-comments',
        'core/latest-posts',
        'core/page-list',
        'core/rss',
        'core/search',
        'core/social-links',
        'core/social-link',
        'core/tag-cloud',
        'core/navigation',
        'core/site-logo',
        'core/site-title',
        'core/site-tagline',
        'core/query',
        'core/posts-list',
        'core/avatar',
        'core/post-title',
        'core/post-excerpt',
        'core/post-featured-image',
        'core/post-content',
        'core/post-author',
        'core/post-date',
        'core/post-terms',
        'core/post-navigation-link',
        'core/read-more',
        'core/comments-query-loop',
        'core/commentspost-comments-form',
        'core/loginout',
        'core/term-description',
        'core/query-title',
        'core/post-author-biography',
        // 'core/embed',
        'core/quote',
        'core/code',
        'core/freeform',
        'core/post-author-name',
        'core/comments',
        'core/post-comments-form',
        'core/template-part',
        'yoast/how-to-block',
        'yoast/faq-block',
        'yoast-seo/breadcrumbs',
    ];

    $post_type = $block_editor_context->post->post_type;

    if ($post_type !== 'cat-template') {
        $blocksToRemove[] = 'acf/categorylist';
    }

    $allowedBlockTypes = array_diff($registerdBblocksArray, $blocksToRemove);
	$allowedBlockTypes = array_values($allowedBlockTypes);

	// Always allow custom webeon-blocks and acf blocks
	$customBlocksToAllow = [];
	foreach ($registerdBblocksArray as $blockName) {
		if (strpos($blockName, 'webeon-blocks/') === 0 || strpos($blockName, 'acf/') === 0) {
			$customBlocksToAllow[] = $blockName;
		}
	}

	// Merge custom blocks with allowed blocks and remove duplicates
	$allowedBlockTypes = array_unique(array_merge($allowedBlockTypes, $customBlocksToAllow));
	$allowedBlockTypes = array_values($allowedBlockTypes);

	return $allowedBlockTypes;

}, 20, 2);

//disable default jquery
add_filter( 'wp_enqueue_scripts', function(){
    if ( !is_admin() ) wp_dequeue_script('jquery');
    if ( !is_admin() ) wp_deregister_script('jquery');
}, PHP_INT_MAX );

//disable different wp_head functions
remove_action( 'wp_head', 'wp_generator');
remove_action( 'wp_head', 'feed_links_extra', 3 );
remove_action( 'wp_head', 'feed_links', 2 );
remove_action( 'wp_head', 'rsd_link' );
remove_action( 'wp_head', 'wlwmanifest_link' );
remove_action( 'wp_head', 'index_rel_link' );
remove_action( 'wp_head', 'start_post_rel_link', 10, 0 );
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0 );
remove_action( 'wp_head', 'parent_post_rel_link', 10, 0 );
remove_action( 'wp_head', 'wp_shortlink_wp_head', 10, 0 );

// REMOVE EMOJI ICONS
remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

add_filter('wpcf7_autop_or_not', '__return_false');

add_filter( 'acf/blocks/wrap_frontend_innerblocks', 'acf_should_wrap_innerblocks', 10, 2 );
function acf_should_wrap_innerblocks( $wrap, $name ) {
    if ( $name == 'acf/test-block' ) {
        return true;
    }
    return false;
}