(function() {
    'use strict';

    // Register the block
    wp.blocks.registerBlockType('webeon-blocks/hero-simple', {
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var message = attributes.message || 'Hello World!';

            return wp.element.createElement(
                'div',
                {
                    className: 'hero-simple wp-block-webeon-blocks-hero-simple'
                },
                wp.element.createElement(
                    'div',
                    { className: 'hero-simple-content' },
                    wp.element.createElement(wp.blockEditor.RichText, {
                        tagName: 'div',
                        className: 'message',
                        value: message,
                        onChange: function(value) {
                            setAttributes({ message: value });
                        },
                        placeholder: 'Enter your hero message...',
                        allowedFormats: ['core/bold', 'core/italic']
                    })
                )
            );
        },

        save: function() {
            // Return null for server-side rendering
            return null;
        }
    });
})();
