<?php

define("ADMIN_AJAX_URL", admin_url("admin-ajax.php"));

define("THEME_PATH", get_template_directory());
define("THEME_URL", get_template_directory_uri());

define("FRONTEND_PATH", THEME_PATH . "/frontend");
define("FRONTEND_URL", THEME_URL . "/frontend");

define("ADMIN_LOCALE", get_user_locale());

function addFilesFromFolder(string $path)
{
    $dir = THEME_PATH . '/' . $path;
    $filesList = scandir($dir);
    foreach ($filesList as $fileName) {
        if ($fileName !== '.' && $fileName !== '..') {
            $filePath = $dir . '/' . $fileName;
            if (is_file($filePath)) {
                include_once($filePath);
            }
        }
    }
}

// classes
addFilesFromFolder('/classes');

function register_layout_category($categories)
    {

        $customBlocksToBegin = [
            [
                'slug'  => 'webeon-blocks',
                'title' => 'WebEon Blocks'
            ],
        ];

        $categoriesSorted = [];

        foreach ($customBlocksToBegin as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        foreach ($categories as $category) {
            $categoriesSorted[] = $category;
        }

        $customBlocksToEnd = [
            [
                'slug'  => 'webeon-service-blocks',
                'title' => 'WebEon Service Blocks'
            ],
        ];

        foreach ($customBlocksToEnd as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        return $categoriesSorted;
        // return $categories;
    }

    add_filter('block_categories_all', 'register_layout_category', 10, 2);

// Register native Gutenberg blocks (independent of ACF)
include_once(THEME_PATH . '/gutenberg/registerBlocks.php');

// Test: Register a simple block directly to verify registration works
function register_test_webeon_block() {
    error_log('=== WEBEON BLOCK REGISTRATION ATTEMPT ===');

    $result = register_block_type('webeon-blocks/simple-test', array(
        'title' => 'Simple Test',
        'description' => 'A simple test block',
        'icon' => 'star-filled',
        'category' => 'common',
        'keywords' => array('test', 'webeon', 'simple'),
        'render_callback' => function() {
            return '<div style="background: #f0f0f0; padding: 20px; border: 2px solid #333;">✅ Webeon Test Block Works!</div>';
        }
    ));

    if ($result) {
        error_log('✅ Block registered successfully: ' . $result->name);
    } else {
        error_log('❌ Block registration failed');
    }

    // Check if function exists
    if (!function_exists('register_block_type')) {
        error_log('❌ register_block_type function does not exist!');
    } else {
        error_log('✅ register_block_type function exists');
    }

    // Check WordPress version
    global $wp_version;
    error_log('WordPress version: ' . $wp_version);

    // Check if we're in admin
    error_log('Is admin: ' . (is_admin() ? 'YES' : 'NO'));

    // Check current action
    error_log('Current action: ' . current_action());
}
add_action('init', 'register_test_webeon_block', 5);

// Additional diagnostic
function webeon_diagnostic() {
    error_log('=== WEBEON DIAGNOSTIC ===');

    // Check if Gutenberg is active
    if (function_exists('register_block_type')) {
        error_log('✅ Gutenberg functions available');

        // Check if Classic Editor is forcing classic mode
        if (function_exists('classic_editor_replace_block_editor') && classic_editor_replace_block_editor()) {
            error_log('⚠️ Classic Editor is forcing classic mode - blocks may not appear');
        }

        // Check if Gutenberg is disabled for this post type
        if (function_exists('use_block_editor_for_post_type')) {
            $post_types = get_post_types(array('public' => true));
            foreach ($post_types as $post_type) {
                $uses_blocks = use_block_editor_for_post_type($post_type);
                error_log("Post type '$post_type' uses block editor: " . ($uses_blocks ? 'YES' : 'NO'));
            }
        }

        // Get all registered blocks
        $registry = WP_Block_Type_Registry::get_instance();
        $all_blocks = $registry->get_all_registered();
        $total_blocks = count($all_blocks);
        error_log('Total blocks registered: ' . $total_blocks);

        // Check for our blocks
        $webeon_blocks = array_filter(array_keys($all_blocks), function($block) {
            return strpos($block, 'webeon-blocks/') === 0;
        });

        if (!empty($webeon_blocks)) {
            error_log('✅ Webeon blocks found: ' . implode(', ', $webeon_blocks));
        } else {
            error_log('❌ No webeon blocks found');
        }

        // Check for core blocks
        $core_blocks = array_filter(array_keys($all_blocks), function($block) {
            return strpos($block, 'core/') === 0;
        });
        error_log('Core blocks count: ' . count($core_blocks));

        // Check for potentially interfering plugins
        if (function_exists('get_option')) {
            $active_plugins = get_option('active_plugins', array());
            $interfering_plugins = array();

            foreach ($active_plugins as $plugin) {
                if (strpos($plugin, 'classic-editor') !== false ||
                    strpos($plugin, 'disable-gutenberg') !== false ||
                    strpos($plugin, 'classic-widgets') !== false) {
                    $interfering_plugins[] = $plugin;
                }
            }

            if (!empty($interfering_plugins)) {
                error_log('⚠️ Potentially interfering plugins: ' . implode(', ', $interfering_plugins));
            } else {
                error_log('✅ No obvious interfering plugins found');
            }
        }

    } else {
        error_log('❌ Gutenberg functions NOT available');
    }
}
add_action('wp_loaded', 'webeon_diagnostic');

// acf
if (function_exists('get_field')) {


    // include_once(THEME_PATH . '/acf/registerOptionsPages.php');
    include_once(THEME_PATH . '/gutenberg/registerBlocks.php');
    addFilesFromFolder('/acf/fields');
    
    // define('GENERAL_SETTINGS', get_field('general_settings_group', 'options'));
    // define('VIEW_SETTINGS', get_field('theme_view_group', 'options'));
    // define('ADDITIONAL_CODE', get_field('additional_code_group', 'options'));
    // define('CONTACTS', get_field('contacts_settings_group', 'options'));

    $fontFamilySetting = VIEW_SETTINGS['theme_font_family'] ?? 'Roboto';
    $secondFontFamilySetting = VIEW_SETTINGS['theme_second_font_family'] ?? 'none';

    $webSafeFonts = [
        'Arial',
        'Tahoma',
        'Helvetica',
    ];

    function convertFontFamilyNameToCss($fontFamilyName){
        return match ($fontFamilyName) {
            'BlenderPro' => 'Blender Pro',
            'Inter' => 'Inter',
            'NotoSans' => 'Noto Sans',
            'OpenSans' => 'Open Sans',
            'Ubuntu' => 'Ubuntu',
            'Montserrat' => 'Montserrat',
            'HelveticaNeue' => 'Helvetica Neue',
            'HelveticaNeueCondensedBold' => 'Helvetica Neue Condensed Bold',
            'Arial' => 'Arial',
            'Tahoma' => 'Tahoma',
            'Helvetica' => 'Helvetica',
            default => 'Roboto',
        };
    }

    $fontFamily = strval(convertFontFamilyNameToCss($fontFamilySetting));
    if(!in_array($fontFamily, $webSafeFonts)){
        $fontsArrayUrls[$fontFamily] = FRONTEND_URL . '/fonts/'. $fontFamilySetting .'/fonts.css';
    }
    $fontsArrayNames[] = $fontFamily;

    if($secondFontFamilySetting && $secondFontFamilySetting != 'none'){
        $secondFontFamily = strval(convertFontFamilyNameToCss($secondFontFamilySetting));
        if(!in_array($secondFontFamily, $webSafeFonts)){
            $fontsArrayUrls[$secondFontFamily] = FRONTEND_URL . '/fonts/'. $secondFontFamilySetting .'/fonts.css';
        }
        $fontsArrayNames[] = $secondFontFamily;
    }

    if(!in_array('Arial', $fontsArrayNames)){
        $fontsArrayNames[] = 'Arial';
    }

    define('FONTSARRAYURLS', $fontsArrayUrls);
    define('FONTSARRAYNAMES', $fontsArrayNames);
}

// wordpress hooks
addFilesFromFolder('/hooks');