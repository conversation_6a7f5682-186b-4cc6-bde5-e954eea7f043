<?php

function registerBlocks() {

    $blocks_dir = __DIR__ . '/blocks/';

    if (!is_dir($blocks_dir)) {
        return;
    }

    $block_folders = array_diff(scandir($blocks_dir), array('..', '.'));

    foreach ($block_folders as $folder) {
        $block_path = $blocks_dir . $folder;

        if (!is_dir($block_path)) {
            continue;
        }

        // Register the block with custom modifications if needed
        if ($folder === 'wrapper') {
            // Register wrapper block with custom SVG icon
            $json_file = $block_path . '/block.json';
            if (file_exists($json_file)) {
                $block_data = json_decode(file_get_contents($json_file), true);
                $block_data['icon'] = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/><rect x="7" y="7" width="10" height="10" rx="1" stroke="currentColor" stroke-width="1" fill="none"/></svg>';
                register_block_type($block_data['name'], $block_data);
            } else {
                register_block_type($block_path);
            }
        } else {
            register_block_type($block_path);
        }
    }

}

add_action( 'init', 'registerBlocks' );


function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');