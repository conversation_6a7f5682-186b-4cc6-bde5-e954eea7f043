/**
 * Wrapper Block Styles
 */
.wp-block-webeon-blocks-wrapper {
    position: relative;
}

.wp-block-webeon-blocks-wrapper .wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.wp-block-webeon-blocks-wrapper .wrapper-content {
    position: relative;
}

/* Block alignment support */
.wp-block-webeon-blocks-wrapper.alignwide {
    max-width: 1400px;
}

.wp-block-webeon-blocks-wrapper.alignfull {
    max-width: none;
    width: 100vw;
    margin-left: calc(50% - 50vw);
}

.wp-block-webeon-blocks-wrapper.alignfull .wrapper {
    max-width: none;
    padding: 0;
}