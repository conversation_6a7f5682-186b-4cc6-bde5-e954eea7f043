<?php

function registerBlocks() {

    $blocks_dir = __DIR__ . '/blocks/';

    // Check if blocks directory exists
    if (!is_dir($blocks_dir)) {
        if (WP_DEBUG) {
            error_log('Webeon Blocks: Directory not found - ' . $blocks_dir);
        }
        return;
    }

    // Get all folders in the blocks directory
    $block_folders = array_diff(scandir($blocks_dir), array('..', '.'));

    foreach ($block_folders as $folder) {
        $block_path = $blocks_dir . $folder;

        // Skip if not a directory
        if (!is_dir($block_path)) {
            continue;
        }

        // Try to register the block
        $result = register_block_type($block_path);

        if ($result) {
            // Successfully registered
            if (WP_DEBUG) {
                error_log('✅ Webeon Block registered: ' . $result->name . ' (from folder: ' . $folder . ')');
            }
        } else {
            // Registration failed
            if (WP_DEBUG) {
                error_log('❌ Failed to register block from folder: ' . $folder);

                // Check what files exist in the folder for debugging
                $files = scandir($block_path);
                $json_exists = in_array($folder . '.json', $files);
                $render_exists = in_array('render.php', $files);

                error_log('   - JSON file (' . $folder . '.json): ' . ($json_exists ? 'EXISTS' : 'MISSING'));
                error_log('   - Render file (render.php): ' . ($render_exists ? 'EXISTS' : 'MISSING'));
            }
        }
    }

    if (WP_DEBUG) {
        error_log('Webeon Blocks: Registration complete. Processed ' . count($block_folders) . ' folders.');
    }

}

add_action( 'init', 'registerBlocks', 5 );

// Test function removed - registration is working

// Uncomment the lines below for debugging if needed
// function webeon_blocks_admin_notice() {
//     if (current_user_can('manage_options')) {
//         $registry = WP_Block_Type_Registry::get_instance();
//         $all_blocks = $registry->get_all_registered();
//
//         if (isset($all_blocks['webeon-blocks/hero-simple'])) {
//             echo '<div class="notice notice-success"><p>✅ Hero Simple block is registered successfully!</p></div>';
//         } else {
//             echo '<div class="notice notice-error"><p>❌ Hero Simple block registration failed!</p></div>';
//         }
//     }
// }
// add_action('admin_notices', 'webeon_blocks_admin_notice');


function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');