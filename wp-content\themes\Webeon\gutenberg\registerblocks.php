<?php

function registerBlocks() {

    $blocks_dir = __DIR__ . '/blocks/';

    if (!is_dir($blocks_dir)) {
        return;
    }

    $block_folders = array_diff(scandir($blocks_dir), array('..', '.'));

    foreach ($block_folders as $folder) {
        $block_path = $blocks_dir . $folder;

        if (!is_dir($block_path)) {
            continue;
        }

        // Register the block
        register_block_type($block_path);
    }

}

add_action( 'init', 'registerBlocks' );

// Custom SVG icons for blocks
function webeon_custom_block_icons() {
    ?>
    <script>
    wp.domReady(function() {
        // Custom SVG icon for wrapper block
        wp.hooks.addFilter(
            'blocks.registerBlockType',
            'webeon-blocks/custom-icons',
            function(settings, name) {
                if (name === 'webeon-blocks/wrapper') {
                    settings.icon = wp.element.createElement('svg', {
                        width: 24,
                        height: 24,
                        viewBox: '0 0 24 24',
                        fill: 'none'
                    }, [
                        wp.element.createElement('rect', {
                            key: 'outer',
                            x: 3,
                            y: 3,
                            width: 18,
                            height: 18,
                            rx: 2,
                            stroke: 'currentColor',
                            strokeWidth: 2,
                            fill: 'none'
                        }),
                        wp.element.createElement('rect', {
                            key: 'inner',
                            x: 7,
                            y: 7,
                            width: 10,
                            height: 10,
                            rx: 1,
                            stroke: 'currentColor',
                            strokeWidth: 1,
                            fill: 'none'
                        })
                    ]);
                }
                return settings;
            }
        );
    });
    </script>
    <?php
}
add_action('admin_footer', 'webeon_custom_block_icons');


function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');