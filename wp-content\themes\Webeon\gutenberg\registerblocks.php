<?php

function registerBlocks() {

    // Register hero-simple block manually with fallback
    $hero_simple_dir = __DIR__ . '/blocks/hero-simple';
    if (is_dir($hero_simple_dir)) {
        $result = register_block_type($hero_simple_dir);

        // Debug: Log block registration
        if (WP_DEBUG) {
            if ($result) {
                error_log('Hero Simple Block registered successfully: ' . $result->name);
            } else {
                error_log('Hero Simple Block registration failed, trying manual registration');

                // Fallback: Manual registration
                $result = register_block_type('webeon-blocks/hero-simple', array(
                    'title' => 'Hero Simple',
                    'description' => 'A simple hero block for displaying prominent messages',
                    'icon' => 'heading',
                    'category' => 'webeon-blocks',
                    'keywords' => array('hero', 'simple', 'webeon'),
                    'render_callback' => function($attributes) {
                        $message = $attributes['message'] ?? 'Hello World!';
                        $wrapper_attributes = get_block_wrapper_attributes(array('class' => 'hero-simple'));

                        return sprintf(
                            '<div %s><div class="hero-simple-content"><div class="message">%s</div></div></div>',
                            $wrapper_attributes,
                            esc_html($message)
                        );
                    },
                    'attributes' => array(
                        'message' => array(
                            'type' => 'string',
                            'default' => 'Hello World!'
                        )
                    ),
                    'supports' => array(
                        'align' => array('wide', 'full')
                    )
                ));

                if ($result) {
                    error_log('Hero Simple Block manual registration successful');
                } else {
                    error_log('Hero Simple Block manual registration also failed');
                }
            }
        }
    }

    // Register other blocks automatically
    $dir = __DIR__ . '/blocks/';
    $folders = array_diff(scandir($dir), array('..', '.', 'hero-simple'));

    foreach ($folders as $folder) {
        if (is_dir($dir . $folder)) {
            $result = register_block_type($dir . $folder);

            // Debug: Log block registration
            if (WP_DEBUG && $result) {
                error_log('Webeon Block registered: ' . $result->name);
            }
        }
    }

}

add_action( 'init', 'registerBlocks', 5 );

// Test function to verify block registration is working
function testBlockRegistration() {
    if (WP_DEBUG) {
        $registry = WP_Block_Type_Registry::get_instance();
        $all_blocks = $registry->get_all_registered();
        $webeon_blocks = array_filter(array_keys($all_blocks), function($block) {
            return strpos($block, 'webeon-blocks/') === 0;
        });

        error_log('Test: Webeon blocks in registry: ' . implode(', ', $webeon_blocks));
    }
}
add_action('init', 'testBlockRegistration', 15);


function blocksIconsSprite() {
    echo file_get_contents(FRONTEND_PATH . '/img/admin-icons-sprite.svg');
    echo file_get_contents(FRONTEND_PATH . '/img/icons-sprites.svg');
}

add_action('in_admin_header', 'blocksIconsSprite');