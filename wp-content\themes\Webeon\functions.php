<?php

define("ADMIN_AJAX_URL", admin_url("admin-ajax.php"));

define("THEME_PATH", get_template_directory());
define("THEME_URL", get_template_directory_uri());

define("FRONTEND_PATH", THEME_PATH . "/frontend");
define("FRONTEND_URL", THEME_URL . "/frontend");

define("ADMIN_LOCALE", get_user_locale());

function addFilesFromFolder(string $path)
{
    $dir = THEME_PATH . '/' . $path;
    $filesList = scandir($dir);
    foreach ($filesList as $fileName) {
        if ($fileName !== '.' && $fileName !== '..') {
            $filePath = $dir . '/' . $fileName;
            if (is_file($filePath)) {
                include_once($filePath);
            }
        }
    }
}

// classes
addFilesFromFolder('/classes');

function register_layout_category($categories)
    {

        $customBlocksToBegin = [
            [
                'slug'  => 'webeon-blocks',
                'title' => 'WebEon Blocks'
            ],
        ];

        $categoriesSorted = [];

        foreach ($customBlocksToBegin as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        foreach ($categories as $category) {
            $categoriesSorted[] = $category;
        }

        $customBlocksToEnd = [
            [
                'slug'  => 'webeon-service-blocks',
                'title' => 'WebEon Service Blocks'
            ],
        ];

        foreach ($customBlocksToEnd as $customBlock) {
            $categoriesSorted[] = $customBlock;
        }

        return $categoriesSorted;
        // return $categories;
    }

    add_filter('block_categories_all', 'register_layout_category', 10, 2);

include_once(THEME_PATH . '/gutenberg/registerblocks.php');

// wordpress hooks
addFilesFromFolder('/hooks');